import time
import ctypes
import sys
import signal

# Configurações do Windows API
user32 = ctypes.windll.user32

# Variável para controlar
pressing = True

def signal_handler(sig, frame):
    """Para o script quando Ctrl+C for pressionado"""
    global pressing
    print("\nParando o script...")
    pressing = False
    release_key()
    sys.exit(0)

def press_key():
    """Pressiona a tecla I"""
    VK_I = 0x49  # Código virtual da tecla I
    user32.keybd_event(VK_I, 0, 0, 0)  # KEYDOWN

def release_key():
    """Libera a tecla I"""
    VK_I = 0x49  # Código virtual da tecla I
    user32.keybd_event(VK_I, 0, 2, 0)  # KEYUP

def press_i_for_lol():
    """Pressiona I especificamente para o League of Legends"""
    print("=== BOT PARA TECLA I NO LEAGUE OF LEGENDS ===")
    print("Este script deve ser executado como ADMINISTRADOR")
    print("Pressione Ctrl+C para parar")
    print("Iniciando em 5 segundos...")
    print("Certifique-se de que o LoL está aberto e com foco!")
    
    time.sleep(5)
    
    try:
        count = 0
        while pressing:
            # Pressiona e solta a tecla I
            press_key()
            time.sleep(0)
              # Intervalo entre pressionamentos
            
           S
    except KeyboardInterrupt:
        print("\nInterrompido pelo usuário")
    except Exception as e:
        print(f"Erro: {e}")
    finally:
        release_key()

if __name__ == "__main__":
    # Verifica se está rodando como administrador
    if not ctypes.windll.shell32.IsUserAnAdmin():
        print("ERRO: Este script deve ser executado como ADMINISTRADOR!")
        print("Feche e execute como: Botão direito -> Executar como administrador")
        input("Pressione Enter para sair...")
        sys.exit(1)
    
    # Configura o handler para Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)
    
    press_i_for_lol()