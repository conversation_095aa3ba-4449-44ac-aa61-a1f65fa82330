import time
import win32gui
import win32api
import win32con
from pynput import keyboard

def test_key_methods():
    """Testa diferentes métodos de envio de tecla"""
    
    print("=== TESTE DE ENVIO DE TECLAS ===")
    print("Este script testará diferentes métodos de envio da tecla 'I'")
    print("Abra o Bloco de Notas ou qualquer editor de texto para ver o resultado")
    print()
    
    # Aguarda 5 segundos para você abrir um editor
    for i in range(5, 0, -1):
        print(f"Iniciando teste em {i} segundos...")
        time.sleep(1)
    
    print("\n1. Testando pynput (método global)...")
    controller = keyboard.Controller()
    
    try:
        # Teste 1: pynput
        controller.press(keyboard.KeyCode.from_char('i'))
        time.sleep(0.1)
        controller.release(keyboard.KeyCode.from_char('i'))
        print("   ✅ pynput funcionou!")
        time.sleep(1)
        
        # Teste 2: Encontrar janela ativa
        print("\n2. Testando envio para janela ativa...")
        active_window = win32gui.GetForegroundWindow()
        if active_window:
            window_title = win32gui.GetWindowText(active_window)
            print(f"   Janela ativa: {window_title}")
            
            # Envia 'i' para a janela ativa
            VK_I = 0x49
            win32api.PostMessage(active_window, win32con.WM_KEYDOWN, VK_I, 0)
            time.sleep(0.1)
            win32api.PostMessage(active_window, win32con.WM_KEYUP, VK_I, 0)
            print("   ✅ PostMessage para janela ativa enviado!")
            time.sleep(1)
            
        # Teste 3: Procurar LoL
        print("\n3. Procurando League of Legends...")
        lol_window = find_lol_window()
        if lol_window:
            print("   ✅ LoL encontrado!")
            # Testa envio para LoL
            win32api.PostMessage(lol_window, win32con.WM_KEYDOWN, VK_I, 0)
            time.sleep(0.1)
            win32api.PostMessage(lol_window, win32con.WM_KEYUP, VK_I, 0)
            print("   ✅ Tecla enviada para LoL!")
        else:
            print("   ❌ LoL não encontrado")
            
    except Exception as e:
        print(f"   ❌ Erro: {e}")
    
    print("\n=== TESTE CONCLUÍDO ===")
    print("Se você viu a letra 'i' aparecer no editor, o método funcionou!")

def find_lol_window():
    """Encontra a janela do LoL"""
    def enum_callback(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            
            lol_indicators = [
                "League of Legends",
                "League of Legends (TM) Client",
                "RiotWindowClass",
                "RCLIENT"
            ]
            
            if any(indicator in title for indicator in lol_indicators) or \
               any(indicator in class_name for indicator in lol_indicators):
                windows.append(hwnd)
                print(f"   Encontrado: {title} (Class: {class_name})")
        return True
    
    windows = []
    win32gui.EnumWindows(enum_callback, windows)
    return windows[0] if windows else None

if __name__ == "__main__":
    test_key_methods()
    input("\nPressione Enter para sair...")
