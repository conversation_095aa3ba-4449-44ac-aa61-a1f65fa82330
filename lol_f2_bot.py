import time
import ctypes
import numpy as np
import cv2
import pyautogui
import keyboard
from PIL import ImageGrab
import win32gui
import win32con

# ========== CONFIGURAÇÕES DO DIRECTINPUT ==========
class KeyBdInput(ctypes.Structure):
    _fields_ = [("wVk", ctypes.c_ushort),
                ("wScan", ctypes.c_ushort),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))]

class HardwareInput(ctypes.Structure):
    _fields_ = [("uMsg", ctypes.c_ulong),
                ("wParamL", ctypes.c_short),
                ("wParamH", ctypes.c_ushort)]

class MouseInput(ctypes.Structure):
    _fields_ = [("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", ctypes.c_ulong),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))]

class Input_I(ctypes.Union):
    _fields_ = [("ki", KeyBdInput),
                ("mi", MouseInput),
                ("hi", HardwareInput)]

class Input(ctypes.Structure):
    _fields_ = [("type", ctypes.c_ulong),
                ("ii", Input_I)]

DIK_I = 0x17

def PressKey(hexKeyCode):
    """Pressiona uma tecla usando DirectInput"""
    extra = ctypes.c_ulong(0)
    ii_ = Input_I()
    ii_.ki = KeyBdInput(0, hexKeyCode, 0x0008, 0, ctypes.pointer(extra))
    x = Input(ctypes.c_ulong(1), ii_)
    ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))

def ReleaseKey(hexKeyCode):
    """Solta uma tecla usando DirectInput"""
    extra = ctypes.c_ulong(0)
    ii_ = Input_I()
    ii_.ki = KeyBdInput(0, hexKeyCode, 0x0008 | 0x0002, 0, ctypes.pointer(extra))
    x = Input(ctypes.c_ulong(1), ii_)
    ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))

# ========== SISTEMA DE VISÃO COMPUTACIONAL ==========
class LoLAutoFollow:
    def __init__(self):
        self.pressing = False
        self.following = False
        self.cycle_count = 0
        self.ally_color_range = {
            'min': np.array([200, 100, 50]),   # Azul claro (aliados)
            'max': np.array([255, 200, 150])
        }
        self.minimap_region = (0, 0, 0, 0)  # Será definido automaticamente
        self.game_window = None
        
    def find_game_window(self):
        """Encontra a janela do League of Legends"""
        def callback(hwnd, extra):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if "League of Legends" in window_text:
                    extra.append(hwnd)
        
        windows = []
        win32gui.EnumWindows(callback, windows)
        
        if windows:
            self.game_window = windows[0]
            rect = win32gui.GetWindowRect(self.game_window)
            print(f"✅ Janela do LoL encontrada: {rect}")
            return True
        return False
    
    def get_minimap_region(self):
        """Define a região do minimapa baseado na resolução da tela"""
        if not self.game_window:
            if not self.find_game_window():
                return None
        
        # Obtém as dimensões da janela do jogo
        x, y, right, bottom = win32gui.GetWindowRect(self.game_window)
        width = right - x
        height = bottom - y
        
        # Calcula a região do minimapa (canto inferior direito)
        minimap_width = int(width * 0.15)  # 15% da largura
        minimap_height = int(height * 0.2)  # 20% da altura
        
        minimap_x = right - minimap_width - 10  # Margem de 10 pixels
        minimap_y = bottom - minimap_height - 10
        
        self.minimap_region = (minimap_x, minimap_y, minimap_width, minimap_height)
        return self.minimap_region
    
    def capture_minimap(self):
        """Captura a tela do minimapa"""
        if self.minimap_region == (0, 0, 0, 0):
            self.get_minimap_region()
        
        screenshot = ImageGrab.grab(bbox=(
            self.minimap_region[0],
            self.minimap_region[1],
            self.minimap_region[0] + self.minimap_region[2],
            self.minimap_region[1] + self.minimap_region[3]
        ))
        
        return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
    
    def find_blue_allies(self, image):
        """Encontra aliados azuis no minimapa"""
        # Converte para HSV para melhor detecção de cor
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Define range para cor azul (aliados)
        lower_blue = np.array([100, 150, 50])
        upper_blue = np.array([140, 255, 255])
        
        # Cria máscara para cor azul
        mask = cv2.inRange(hsv, lower_blue, upper_blue)
        
        # Encontra contornos
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        allies = []
        for contour in contours:
            if cv2.contourArea(contour) > 5:  # Filtra contornos muito pequenos
                x, y, w, h = cv2.boundingRect(contour)
                allies.append((x + w//2, y + h//2))  # Centro do contorno
        
        return allies, mask
    
    def click_on_ally(self, ally_position):
        """Clica no aliado encontrado"""
        # Converte coordenadas do minimapa para coordenadas da tela
        screen_x = self.minimap_region[0] + ally_position[0]
        screen_y = self.minimap_region[1] + ally_position[1]
        
        # Salva posição atual do mouse
        original_pos = pyautogui.position()
        
        try:
            # Move e clica no aliado
            pyautogui.moveTo(screen_x, screen_y, duration=0.1)
            pyautogui.click()
            
            # Volta o mouse para posição original
            pyautogui.moveTo(original_pos.x, original_pos.y, duration=0.1)
            
            print(f"✅ Clicou no aliado em ({screen_x}, {screen_y})")
            return True
            
        except Exception as e:
            print(f"❌ Erro ao clicar: {e}")
            return False
    
    def follow_nearest_ally(self):
        """Encontra e segue o aliado mais próximo do centro"""
        minimap_img = self.capture_minimap()
        allies, mask = self.find_blue_allies(minimap_img)
        
        if not allies:
            print("🔍 Nenhum aliado azul encontrado no minimapa")
            return False
        
        # Encontra o aliado mais próximo do centro do minimapa
        center_x, center_y = self.minimap_region[2] // 2, self.minimap_region[3] // 2
        nearest_ally = min(allies, key=lambda pos: ((pos[0] - center_x) ** 2 + (pos[1] - center_y) ** 2) ** 0.5)
        
        print(f"🎯 Aliados encontrados: {len(allies)} | Seguindo o mais próximo")
        return self.click_on_ally(nearest_ally)
    
    def check_key_press(self):
        """Verifica se F10 foi pressionado para ligar/desligar"""
        VK_F10 = 0x79
        if ctypes.windll.user32.GetAsyncKeyState(VK_F10) & 0x8000:
            self.pressing = not self.pressing
            status = "LIGADO" if self.pressing else "DESLIGADO"
            print(f"\n🔧 Bot {status} (F10)")
            time.sleep(0.5)
            return True
        return False
    
    def check_exit_key(self):
        """Verifica se F9 foi pressionado para sair"""
        VK_F9 = 0x78
        if ctypes.windll.user32.GetAsyncKeyState(VK_F9) & 0x8000:
            print("\n🛑 Saindo do programa...")
            return True
        return False
    
    def run(self):
        """Executa o sistema principal"""
        print("=== SISTEMA AUTO-FOLLOW LOL ===")
        print("Requisitos:")
        print("- League of Legends em modo TELA CHEIA ou JANELA SEM BARDA")
        print("- Resolução 1920x1080 recomendada")
        print("- Aliados devem aparecer AZUIS no minimapa")
        print()
        print("Teclas de controle:")
        print("  F10 - Ligar/Desligar o bot")
        print("  F9  - Sair do programa")
        print()
        
        # Tenta encontrar a janela do jogo
        if not self.find_game_window():
            print("❌ Janela do League of Legends não encontrada!")
            print("Certifique-se de que o jogo está aberto e visível")
            return
        
        print("✅ Pronto! Pressione F10 para iniciar...")
        
        try:
            while True:
                if self.check_exit_key():
                    break
                
                if self.check_key_press():
                    if self.pressing:
                        self.cycle_count += 1
                        print(f"\n🔄 Ciclo {self.cycle_count} - Iniciando...")
                
                if self.pressing:
                    # Fase 1: Pressiona I para seguir aliado
                    print("🎮 Pressionando tecla I...")
                    PressKey(DIK_I)
                    time.sleep(0.1)
                    ReleaseKey(DIK_I)
                    
                    # Fase 2: Usa visão computacional para encontrar e clicar em aliados
                    print("👀 Procurando aliados no minimapa...")
                    if self.follow_nearest_ally():
                        self.following = True
                        print("✅ Seguindo aliado com sucesso!")
                    else:
                        print("⚠️  Não foi possível encontrar aliado, tentando apenas tecla I")
                    
                    # Mantém o ciclo por um tempo
                    for i in range(5, 0, -1):
                        print(f"   Próxima ação em {i}s...", end='\r')
                        
                        # Verifica teclas durante a espera
                        for _ in range(10):
                            if self.check_exit_key():
                                return
                            if self.check_key_press():
                                if not self.pressing:
                                    print("\n⏸️  Bot pausado")
                                    break
                            time.sleep(0.1)
                        
                        if not self.pressing:
                            break
                    
                    print("\n" + "="*50)
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n\n⏹️  Interrompido pelo usuário")
        except Exception as e:
            print(f"\n❌ Erro: {e}")
        finally:
            ReleaseKey(DIK_I)
            print("\n=== PROGRAMA FINALIZADO ===")

# ========== INSTALAÇÃO DE DEPENDÊNCIAS ==========
def install_requirements():
    """Instala as dependências necessárias"""
    requirements = [
        "opencv-python",
        "pyautogui",
        "pillow",
        "numpy",
        "keyboard",
        "pywin32"
    ]
    
    print("Instalando dependências...")
    for package in requirements:
        try:
            __import__(package.split('-')[0])
            print(f"✅ {package} já instalado")
        except ImportError:
            print(f"📦 Instalando {package}...")
            import subprocess
            subprocess.check_call(["pip", "install", package])

# ========== EXECUÇÃO PRINCIPAL ==========
if __name__ == "__main__":
    # Verifica e instala dependências
    try:
        install_requirements()
    except:
        print("⚠️  Erro ao instalar dependências, tentando continuar...")
    
    # Executa o sistema
    bot = LoLAutoFollow()
    bot.run()
    input("\nPressione Enter para sair...")