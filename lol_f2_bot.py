import tkinter as tk
from tkinter import ttk
import threading
import time
from pynput import keyboard
from pynput.keyboard import Key, Listener
import sys

class LoLF2Bot:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("LoL F2 Bot")
        self.root.geometry("300x200")
        self.root.resizable(False, False)
        
        # Estado do bot
        self.is_active = False
        self.f2_thread = None
        self.controller = keyboard.Controller()
        
        # Configurar interface
        self.setup_ui()
        
        # Configurar hotkey para parar (ESC)
        self.setup_hotkeys()
        
    def setup_ui(self):
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Título
        title_label = ttk.Label(main_frame, text="League of Legends F2 Bot", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Status
        self.status_label = ttk.Label(main_frame, text="Status: Inativo", 
                                     font=("Arial", 10))
        self.status_label.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        # Botões
        self.start_button = ttk.Button(main_frame, text="Ativar F2", 
                                      command=self.toggle_bot, width=15)
        self.start_button.grid(row=2, column=0, padx=(0, 5))
        
        self.stop_button = ttk.Button(main_frame, text="Parar", 
                                     command=self.stop_bot, width=15)
        self.stop_button.grid(row=2, column=1, padx=(5, 0))
        
        # Informações
        info_label = ttk.Label(main_frame, text="Pressione ESC para parar rapidamente", 
                              font=("Arial", 8), foreground="gray")
        info_label.grid(row=3, column=0, columnspan=2, pady=(20, 0))
        
        # Instruções
        instructions = ttk.Label(main_frame, 
                               text="O bot irá segurar F2 continuamente\nquando ativado",
                               font=("Arial", 9), justify="center")
        instructions.grid(row=4, column=0, columnspan=2, pady=(10, 0))
        
    def setup_hotkeys(self):
        """Configura hotkeys globais"""
        def on_press(key):
            try:
                if key == Key.esc and self.is_active:
                    self.stop_bot()
            except AttributeError:
                pass
        
        # Listener para hotkeys em thread separada
        self.hotkey_listener = Listener(on_press=on_press)
        self.hotkey_listener.daemon = True
        self.hotkey_listener.start()
        
    def f2_loop(self):
        """Loop principal que mantém F2 pressionado"""
        try:
            # Pressiona F2
            self.controller.press(Key.f2)
            
            while self.is_active:
                time.sleep(0.1)  # Pequena pausa para não sobrecarregar
                
        except Exception as e:
            print(f"Erro no loop F2: {e}")
        finally:
            # Garante que F2 seja liberado
            try:
                self.controller.release(Key.f2)
            except:
                pass
                
    def toggle_bot(self):
        """Alterna entre ativar e desativar o bot"""
        if not self.is_active:
            self.start_bot()
        else:
            self.stop_bot()
            
    def start_bot(self):
        """Inicia o bot"""
        if not self.is_active:
            self.is_active = True
            self.status_label.config(text="Status: Ativo (F2 pressionado)")
            self.start_button.config(text="Desativar F2")
            
            # Inicia thread do F2
            self.f2_thread = threading.Thread(target=self.f2_loop, daemon=True)
            self.f2_thread.start()
            
    def stop_bot(self):
        """Para o bot"""
        if self.is_active:
            self.is_active = False
            self.status_label.config(text="Status: Inativo")
            self.start_button.config(text="Ativar F2")
            
            # Libera F2 imediatamente
            try:
                self.controller.release(Key.f2)
            except:
                pass
                
    def on_closing(self):
        """Chamado quando a janela é fechada"""
        self.stop_bot()
        self.hotkey_listener.stop()
        self.root.destroy()
        
    def run(self):
        """Executa a aplicação"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = LoLF2Bot()
        app.run()
    except KeyboardInterrupt:
        print("Bot interrompido pelo usuário")
    except Exception as e:
        print(f"Erro: {e}")
        input("Pressione Enter para sair...")
