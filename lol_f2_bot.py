import time
import ctypes
import tkinter as tk
from tkinter import ttk
import threading
import sys

# Importar para hotkeys (opcional)
try:
    from pynput.keyboard import Key, Listener
    HOTKEY_AVAILABLE = True
except ImportError:
    HOTKEY_AVAILABLE = False
    print("Aviso: pynput não instalado. Hotkey ESC não disponível.")

# DirectInput structures (copiado do directinput.py)
class KeyBdInput(ctypes.Structure):
    _fields_ = [("wVk", ctypes.c_ushort),
                ("wScan", ctypes.c_ushort),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))]

class HardwareInput(ctypes.Structure):
    _fields_ = [("uMsg", ctypes.c_ulong),
                ("wParamL", ctypes.c_short),
                ("wParamH", ctypes.c_ushort)]

class MouseInput(ctypes.Structure):
    _fields_ = [("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", ctypes.c_ulong),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))]

class Input_I(ctypes.Union):
    _fields_ = [("ki", KeyBdInput),
                ("mi", MouseInput),
                ("hi", HardwareInput)]

class Input(ctypes.Structure):
    _fields_ = [("type", ctypes.c_ulong),
                ("ii", Input_I)]

# Código DirectInput para a tecla I
DIK_I = 0x17

def PressKey(hexKeyCode):
    """Pressiona uma tecla usando DirectInput"""
    extra = ctypes.c_ulong(0)
    ii_ = Input_I()
    ii_.ki = KeyBdInput(0, hexKeyCode, 0x0008, 0, ctypes.pointer(extra))
    x = Input(ctypes.c_ulong(1), ii_)
    ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))

def ReleaseKey(hexKeyCode):
    """Solta uma tecla usando DirectInput"""
    extra = ctypes.c_ulong(0)
    ii_ = Input_I()
    ii_.ki = KeyBdInput(0, hexKeyCode, 0x0008 | 0x0002, 0, ctypes.pointer(extra))
    x = Input(ctypes.c_ulong(1), ii_)
    ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))

class LoLDirectInputBot:
    def __init__(self):
        # Configurar janela principal
        self.root = tk.Tk()
        self.root.title("LoL DirectInput I Bot")
        self.root.geometry("400x300")
        self.root.resizable(False, False)

        # Estado do bot
        self.is_active = False
        self.bot_thread = None
        self.hotkey_listener = None

        # Configurar interface
        self.setup_ui()

        # Configurar hotkey ESC (se disponível)
        self.setup_hotkey()

        # Configurar fechamento da janela
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_ui(self):
        """Configura a interface gráfica"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Título
        title_label = ttk.Label(main_frame, text="LoL DirectInput I Bot",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Status
        self.status_label = ttk.Label(main_frame, text="Status: Inativo",
                                     font=("Arial", 12))
        self.status_label.grid(row=1, column=0, columnspan=2, pady=(0, 10))

        # Status do DirectInput
        self.directinput_label = ttk.Label(main_frame, text="DirectInput: Pronto",
                                          font=("Arial", 10), foreground="green")
        self.directinput_label.grid(row=2, column=0, columnspan=2, pady=(0, 20))

        # Botões principais
        self.start_button = ttk.Button(main_frame, text="🎮 Ativar Bot",
                                      command=self.toggle_bot, width=20)
        self.start_button.grid(row=3, column=0, padx=(0, 10))

        self.stop_button = ttk.Button(main_frame, text="⏹️ Parar Bot",
                                     command=self.stop_bot, width=20)
        self.stop_button.grid(row=3, column=1, padx=(10, 0))

        # Botão de teste
        self.test_button = ttk.Button(main_frame, text="🧪 Testar DirectInput (3s)",
                                     command=self.test_directinput, width=42)
        self.test_button.grid(row=4, column=0, columnspan=2, pady=(20, 0))

        # Informações
        esc_text = "• Pressione ESC para parar rapidamente" if HOTKEY_AVAILABLE else "• Hotkey ESC não disponível"
        info_text = f"{esc_text}\n• Funciona em qualquer jogo\n• Não precisa que o jogo esteja em foco"
        info_label = ttk.Label(main_frame, text=info_text,
                              font=("Arial", 9), justify="center")
        info_label.grid(row=5, column=0, columnspan=2, pady=(20, 0))

        # Instruções
        instructions = ttk.Label(main_frame,
                               text="1. Abra o LoL (ou qualquer jogo)\n2. Clique 'Ativar Bot'\n3. A tecla I ficará pressionada!",
                               font=("Arial", 10), justify="center", foreground="blue")
        instructions.grid(row=6, column=0, columnspan=2, pady=(15, 0))

    def setup_hotkey(self):
        """Configura hotkey ESC para parar o bot"""
        if not HOTKEY_AVAILABLE:
            return

        def on_press(key):
            try:
                if key == Key.esc and self.is_active:
                    print("🔥 ESC pressionado - Parando bot...")
                    self.stop_bot()
            except AttributeError:
                pass

        # Inicia listener em thread separada
        try:
            self.hotkey_listener = Listener(on_press=on_press)
            self.hotkey_listener.daemon = True
            self.hotkey_listener.start()
            print("⌨️ Hotkey ESC configurada!")
        except Exception as e:
            print(f"Aviso: Não foi possível configurar hotkey ESC: {e}")

    def bot_loop(self):
        """Loop principal que mantém a tecla I pressionada"""
        try:
            print("🎮 Bot iniciado - Pressionando tecla I...")
            PressKey(DIK_I)

            count = 0
            while self.is_active:
                time.sleep(0.1)
                count += 1

                # Re-pressiona a cada 2 segundos para garantir
                if count % 20 == 0:
                    PressKey(DIK_I)

                # Debug a cada 50 iterações (5 segundos)
                if count % 50 == 0:
                    print(f"   Bot ativo há {count // 10} segundos...")

        except Exception as e:
            print(f"❌ Erro no bot: {e}")
        finally:
            # Sempre libera a tecla ao sair
            try:
                ReleaseKey(DIK_I)
                print("🛑 Tecla I liberada!")
            except:
                pass

    def toggle_bot(self):
        """Alterna entre ativar e desativar o bot"""
        if not self.is_active:
            self.start_bot()
        else:
            self.stop_bot()

    def start_bot(self):
        """Inicia o bot"""
        if not self.is_active:
            self.is_active = True
            self.status_label.config(text="Status: 🟢 ATIVO - Tecla I pressionada")
            self.start_button.config(text="🔄 Desativar Bot")
            self.directinput_label.config(text="DirectInput: Funcionando", foreground="blue")

            # Inicia thread do bot
            self.bot_thread = threading.Thread(target=self.bot_loop, daemon=True)
            self.bot_thread.start()

            print("🚀 Bot ativado!")

    def stop_bot(self):
        """Para o bot"""
        if self.is_active:
            self.is_active = False
            self.status_label.config(text="Status: 🔴 INATIVO")
            self.start_button.config(text="🎮 Ativar Bot")
            self.directinput_label.config(text="DirectInput: Pronto", foreground="green")

            # Libera a tecla imediatamente
            try:
                ReleaseKey(DIK_I)
                print("🛑 Bot desativado!")
            except:
                pass

    def test_directinput(self):
        """Testa DirectInput por 3 segundos"""
        def test_thread():
            try:
                self.directinput_label.config(text="DirectInput: Testando...", foreground="orange")
                print("🧪 Testando DirectInput por 3 segundos...")

                PressKey(DIK_I)
                print("✅ Tecla I pressionada!")

                for i in range(3, 0, -1):
                    print(f"   Teste: {i}s restantes...")
                    time.sleep(1)

                ReleaseKey(DIK_I)
                print("✅ Teste concluído - tecla liberada!")
                self.directinput_label.config(text="DirectInput: Funcionando!", foreground="green")

            except Exception as e:
                print(f"❌ Erro no teste: {e}")
                self.directinput_label.config(text="DirectInput: Erro", foreground="red")

        # Executa teste em thread separada para não travar a interface
        threading.Thread(target=test_thread, daemon=True).start()

    def on_closing(self):
        """Chamado quando a janela é fechada"""
        if self.is_active:
            self.stop_bot()

        # Para o hotkey listener
        if self.hotkey_listener and HOTKEY_AVAILABLE:
            try:
                self.hotkey_listener.stop()
            except:
                pass

        self.root.destroy()
        print("👋 Bot encerrado!")

    def run(self):
        """Executa a aplicação"""
        print("🚀 Iniciando LoL DirectInput I Bot...")
        print("📋 Interface gráfica aberta!")
        self.root.mainloop()

if __name__ == "__main__":
    try:
        bot = LoLDirectInputBot()
        bot.run()
    except KeyboardInterrupt:
        print("\n👋 Bot interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro: {e}")
        input("Pressione Enter para sair...")
