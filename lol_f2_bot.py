import tkinter as tk
from tkinter import ttk
import threading
import time
from pynput import keyboard
from pynput.keyboard import Key, Listener
import sys
import ctypes
from ctypes import wintypes
import win32gui
import win32con
import win32api

class LoLF2Bot:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("LoL I Bot")
        self.root.geometry("350x250")
        self.root.resizable(False, False)

        # Estado do bot
        self.is_active = False
        self.i_thread = None
        self.controller = keyboard.Controller()
        self.lol_window = None

        # Códigos de tecla do Windows
        self.VK_I = 0x49  # C<PERSON>digo da tecla 'i'
        self.KEYEVENTF_KEYUP = 0x0002

        # Configurar interface
        self.setup_ui()

        # Configurar hotkey para parar (ESC)
        self.setup_hotkeys()
        
    def setup_ui(self):
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Título
        title_label = ttk.Label(main_frame, text="League of Legends I Bot",
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Status
        self.status_label = ttk.Label(main_frame, text="Status: Inativo",
                                     font=("Arial", 10))
        self.status_label.grid(row=1, column=0, columnspan=2, pady=(0, 10))

        # Status do LoL
        self.lol_status_label = ttk.Label(main_frame, text="LoL: Não detectado",
                                         font=("Arial", 9), foreground="red")
        self.lol_status_label.grid(row=2, column=0, columnspan=2, pady=(0, 10))
        
        # Botões
        self.start_button = ttk.Button(main_frame, text="Ativar I",
                                      command=self.toggle_bot, width=15)
        self.start_button.grid(row=3, column=0, padx=(0, 5))

        self.stop_button = ttk.Button(main_frame, text="Parar",
                                     command=self.stop_bot, width=15)
        self.stop_button.grid(row=3, column=1, padx=(5, 0))

        # Botão para detectar LoL
        self.detect_button = ttk.Button(main_frame, text="Detectar LoL",
                                       command=self.detect_lol_window, width=32)
        self.detect_button.grid(row=4, column=0, columnspan=2, pady=(10, 0))
        
        # Informações
        info_label = ttk.Label(main_frame, text="Pressione ESC para parar rapidamente",
                              font=("Arial", 8), foreground="gray")
        info_label.grid(row=5, column=0, columnspan=2, pady=(20, 0))

        # Instruções
        instructions = ttk.Label(main_frame,
                               text="1. Clique 'Detectar LoL' primeiro\n2. Entre na partida\n3. Ative o I",
                               font=("Arial", 9), justify="center")
        instructions.grid(row=6, column=0, columnspan=2, pady=(10, 0))
        
    def setup_hotkeys(self):
        """Configura hotkeys globais"""
        def on_press(key):
            try:
                if key == Key.esc and self.is_active:
                    self.stop_bot()
            except AttributeError:
                pass
        
        # Listener para hotkeys em thread separada
        self.hotkey_listener = Listener(on_press=on_press)
        self.hotkey_listener.daemon = True
        self.hotkey_listener.start()

    def detect_lol_window(self):
        """Detecta a janela do League of Legends"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                # Procura por diferentes variações do LoL
                lol_indicators = [
                    "League of Legends",
                    "League of Legends (TM) Client",
                    "RiotWindowClass",
                    "RCLIENT"
                ]
                if any(indicator in window_title for indicator in lol_indicators) or \
                   any(indicator in class_name for indicator in lol_indicators):
                    windows.append((hwnd, window_title, class_name))
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        if windows:
            # Pega a primeira janela do LoL encontrada
            self.lol_window = windows[0][0]
            title = windows[0][1] if windows[0][1] else windows[0][2]
            self.lol_status_label.config(text=f"LoL: Detectado - {title[:25]}...",
                                        foreground="green")
            print(f"LoL detectado: {title} (Class: {windows[0][2]})")
            return True
        else:
            self.lol_window = None
            self.lol_status_label.config(text="LoL: Não encontrado", foreground="red")
            print("LoL não encontrado. Certifique-se de que o jogo está aberto.")
            return False

    def send_key_to_lol(self, vk_code, key_up=False):
        """Envia tecla diretamente para a janela do LoL"""
        if not self.lol_window:
            return False

        try:
            # Verifica se a janela ainda existe
            if not win32gui.IsWindow(self.lol_window):
                print("Janela do LoL não existe mais, tentando detectar novamente...")
                self.detect_lol_window()
                if not self.lol_window:
                    return False

            # Tenta diferentes métodos de envio
            success = False

            # Método 1: PostMessage
            try:
                if key_up:
                    win32api.PostMessage(self.lol_window, win32con.WM_KEYUP, vk_code, 0)
                else:
                    win32api.PostMessage(self.lol_window, win32con.WM_KEYDOWN, vk_code, 0)
                success = True
            except:
                pass

            # Método 2: SendMessage (se PostMessage falhar)
            if not success:
                try:
                    if key_up:
                        win32api.SendMessage(self.lol_window, win32con.WM_KEYUP, vk_code, 0)
                    else:
                        win32api.SendMessage(self.lol_window, win32con.WM_KEYDOWN, vk_code, 0)
                    success = True
                except:
                    pass

            return success
        except Exception as e:
            print(f"Erro ao enviar tecla: {e}")
            return False
        
    def i_loop(self):
        """Loop principal que mantém I pressionado"""
        try:
            if self.lol_window:
                print("Iniciando envio de tecla I para LoL...")
                # Envia I para a janela do LoL
                success = self.send_key_to_lol(self.VK_I, key_up=False)
                if success:
                    print("Primeira tecla I enviada com sucesso!")
                else:
                    print("Falha ao enviar primeira tecla I")

                count = 0
                while self.is_active:
                    time.sleep(0.05)  # Envia I repetidamente para manter pressionado
                    if self.is_active:  # Verifica novamente antes de enviar
                        success = self.send_key_to_lol(self.VK_I, key_up=False)
                        count += 1
                        if count % 100 == 0:  # Debug a cada 5 segundos
                            print(f"Tecla I enviada {count} vezes...")
            else:
                print("LoL não detectado, usando método global...")
                # Fallback para método global se LoL não foi detectado
                self.controller.press(keyboard.KeyCode.from_char('i'))
                while self.is_active:
                    time.sleep(0.1)

        except Exception as e:
            print(f"Erro no loop I: {e}")
        finally:
            # Garante que I seja liberado
            try:
                if self.lol_window:
                    self.send_key_to_lol(self.VK_I, key_up=True)
                else:
                    self.controller.release(keyboard.KeyCode.from_char('i'))
            except:
                pass
                
    def toggle_bot(self):
        """Alterna entre ativar e desativar o bot"""
        if not self.is_active:
            self.start_bot()
        else:
            self.stop_bot()
            
    def start_bot(self):
        """Inicia o bot"""
        if not self.is_active:
            # Tenta detectar LoL automaticamente se não foi detectado
            if not self.lol_window:
                self.detect_lol_window()

            self.is_active = True
            if self.lol_window:
                self.status_label.config(text="Status: Ativo (I → LoL)")
            else:
                self.status_label.config(text="Status: Ativo (I global)")
            self.start_button.config(text="Desativar I")

            # Inicia thread do I
            self.i_thread = threading.Thread(target=self.i_loop, daemon=True)
            self.i_thread.start()
            
    def stop_bot(self):
        """Para o bot"""
        if self.is_active:
            self.is_active = False
            self.status_label.config(text="Status: Inativo")
            self.start_button.config(text="Ativar I")

            # Libera I imediatamente
            try:
                if self.lol_window:
                    self.send_key_to_lol(self.VK_I, key_up=True)
                else:
                    self.controller.release(keyboard.KeyCode.from_char('i'))
            except:
                pass
                
    def on_closing(self):
        """Chamado quando a janela é fechada"""
        self.stop_bot()
        self.hotkey_listener.stop()
        self.root.destroy()
        
    def run(self):
        """Executa a aplicação"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = LoLF2Bot()
        app.run()
    except KeyboardInterrupt:
        print("Bot interrompido pelo usuário")
    except Exception as e:
        print(f"Erro: {e}")
        input("Pressione Enter para sair...")
