import time
import ctypes

# DirectInput structures (copiado do directinput.py)
class KeyBdInput(ctypes.Structure):
    _fields_ = [("wVk", ctypes.c_ushort),
                ("wScan", ctypes.c_ushort),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))]

class HardwareInput(ctypes.Structure):
    _fields_ = [("uMsg", ctypes.c_ulong),
                ("wParamL", ctypes.c_short),
                ("wParamH", ctypes.c_ushort)]

class MouseInput(ctypes.Structure):
    _fields_ = [("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", ctypes.c_ulong),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))]

class Input_I(ctypes.Union):
    _fields_ = [("ki", KeyBdInput),
                ("mi", MouseInput),
                ("hi", HardwareInput)]

class Input(ctypes.Structure):
    _fields_ = [("type", ctypes.c_ulong),
                ("ii", Input_I)]

# Código DirectInput para a tecla I
DIK_I = 0x17

def PressKey(hexKeyCode):
    """Pressiona uma tecla usando DirectInput"""
    extra = ctypes.c_ulong(0)
    ii_ = Input_I()
    ii_.ki = KeyBdInput(0, hexKeyCode, 0x0008, 0, ctypes.pointer(extra))
    x = Input(ctypes.c_ulong(1), ii_)
    ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))

def ReleaseKey(hexKeyCode):
    """Solta uma tecla usando DirectInput"""
    extra = ctypes.c_ulong(0)
    ii_ = Input_I()
    ii_.ki = KeyBdInput(0, hexKeyCode, 0x0008 | 0x0002, 0, ctypes.pointer(extra))
    x = Input(ctypes.c_ulong(1), ii_)
    ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))

def test_directinput():
    """Testa DirectInput mantendo I pressionado"""
    print("=== TESTE DIRECTINPUT SIMPLES ===")
    print("Abra o Bloco de Notas ou LoL para testar")
    print("A tecla I será mantida pressionada por 5 segundos")
    print()
    
    # Countdown
    for i in range(5, 0, -1):
        print(f"Iniciando em {i} segundos...")
        time.sleep(1)
    
    print("\n🔄 Pressionando tecla I...")
    
    try:
        # Pressiona I
        PressKey(DIK_I)
        print("✅ Tecla I pressionada com DirectInput!")
        
        # Mantém por 5 segundos
        for i in range(5, 0, -1):
            print(f"   Mantendo pressionado... {i}s restantes")
            time.sleep(1)
        
        # Libera I
        ReleaseKey(DIK_I)
        print("✅ Tecla I liberada!")
        
    except Exception as e:
        print(f"❌ Erro: {e}")
    
    print("\n=== TESTE CONCLUÍDO ===")
    print("Se funcionou, você deve ter visto muitas letras 'i' no editor!")

if __name__ == "__main__":
    test_directinput()
    input("\nPressione Enter para sair...")
