# LoL F2 Bot

Um bot simples para League of Legends que mantém a tecla F2 pressionada continuamente.

## Funcionalidades

- ✅ Pressiona e mantém a tecla F2 pressionada
- ✅ Interface gráfica intuitiva para ativar/desativar
- ✅ Hotkey ESC para parar rapidamente
- ✅ Seguro - libera a tecla automaticamente ao parar

## Instalação

1. Certifique-se de ter Python 3.7+ instalado
2. Instale as dependências:
```bash
pip install -r requirements.txt
```

## Como usar

1. Execute o script:
```bash
python lol_f2_bot.py
```

2. Uma janela será aberta com os controles do bot

3. Clique em "Ativar F2" para começar a pressionar F2 continuamente

4. Para parar:
   - Clique em "Parar" na interface
   - Ou pressione ESC (hotkey global)
   - Ou feche a janela

## Controles

- **Ativar F2**: Inicia o bot e mantém F2 pressionado
- **Parar**: Para o bot e libera a tecla F2
- **ESC**: Hotkey global para parar rapidamente

## Segurança

- O bot libera automaticamente a tecla F2 quando parado
- Funciona apenas enquanto a aplicação estiver rodando
- Não interfere com outros jogos ou aplicações

## Requisitos

- Python 3.7+
- Windows/Linux/macOS
- Biblioteca pynput

## Aviso

Use este bot de acordo com os termos de serviço do League of Legends. O autor não se responsabiliza pelo uso inadequado da ferramenta.
