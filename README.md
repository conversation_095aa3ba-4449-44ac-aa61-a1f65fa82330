# LoL DirectInput I Bot

Um bot avançado para League of Legends que usa DirectInput para manter a tecla I pressionada continuamente.

## Funcionalidades

- ✅ **DirectInput**: Funciona em jogos que bloqueiam input normal
- ✅ **Sem dependências**: Usa apenas Python nativo + ctypes
- ✅ **Interface gráfica**: Controle fácil e intuitivo
- ✅ **Hotkey ESC**: Para rapidamente em emergências
- ✅ **Seguro**: Libera a tecla automaticamente ao parar
- ✅ **Teste integrado**: Botão para testar antes de usar

## Instalação

1. Certifique-se de ter Python 3.7+ instalado
2. **Não precisa instalar nada mais!** O bot usa apenas bibliotecas nativas do Python

## Como usar

1. Execute o script:
```bash
python lol_f2_bot.py
```

2. Uma janela será aberta com os controles do bot

3. **Opcional**: Clique em "Testar DirectInput" para verificar se funciona

4. Abra o League of Legends (ou qualquer jogo)

5. Clique em "Ativar I" para começar a pressionar I continuamente

6. Para parar:
   - Clique em "Parar" na interface
   - Ou pressione ESC (hotkey global)
   - Ou feche a janela

## Controles

- **Testar DirectInput**: Testa o DirectInput por 3 segundos
- **Ativar I**: Inicia o bot e mantém I pressionado
- **Parar**: Para o bot e libera a tecla I
- **ESC**: Hotkey global para parar rapidamente

## Recursos Avançados

- **DirectInput nativo**: Funciona em qualquer jogo, incluindo LoL
- **Sem bloqueios**: Não é bloqueado por anti-cheat básico
- **Funciona em tela cheia**: Não precisa que o jogo esteja em janela
- **Zero dependências**: Só precisa do Python padrão
- **Status visual**: Mostra o estado do DirectInput em tempo real

## Segurança

- O bot libera automaticamente a tecla F2 quando parado
- Funciona apenas enquanto a aplicação estiver rodando
- Não interfere com outros jogos ou aplicações

## Requisitos

- Python 3.7+
- Windows (para DirectInput)
- **Nenhuma biblioteca externa necessária!**

## Aviso

Use este bot de acordo com os termos de serviço do League of Legends. O autor não se responsabiliza pelo uso inadequado da ferramenta.
