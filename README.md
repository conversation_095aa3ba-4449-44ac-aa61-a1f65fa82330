# LoL I Bot

Um bot simples para League of Legends que mantém a tecla I pressionada continuamente.

## Funcionalidades

- ✅ Pressiona e mantém a tecla I pressionada
- ✅ Interface gráfica intuitiva para ativar/desativar
- ✅ Hotkey ESC para parar rapidamente
- ✅ Seguro - libera a tecla automaticamente ao parar

## Instalação

1. Certifique-se de ter Python 3.7+ instalado
2. Instale as dependências:
```bash
pip install -r requirements.txt
```

## Como usar

1. Execute o script:
```bash
python lol_f2_bot.py
```

2. Uma janela será aberta com os controles do bot

3. **IMPORTANTE**: Clique em "Detectar LoL" para encontrar a janela do League of Legends

4. Entre na partida do League of Legends

5. Clique em "Ativar I" para começar a pressionar I continuamente

6. Para parar:
   - Clique em "Parar" na interface
   - Ou pressione ESC (hotkey global)
   - Ou feche a janela

## Controles

- **Detectar LoL**: Encontra a janela do League of Legends
- **Ativar I**: Inicia o bot e mantém I pressionado
- **Parar**: Para o bot e libera a tecla I
- **ESC**: Hotkey global para parar rapidamente

## Recursos Avançados

- **Detecção automática**: O bot detecta a janela do LoL automaticamente
- **Envio direto**: Envia I diretamente para o jogo, mesmo quando não está em foco
- **Fallback global**: Se não detectar o LoL, funciona globalmente
- **Status visual**: Mostra se o LoL foi detectado ou não

## Segurança

- O bot libera automaticamente a tecla F2 quando parado
- Funciona apenas enquanto a aplicação estiver rodando
- Não interfere com outros jogos ou aplicações

## Requisitos

- Python 3.7+
- Windows (necessário para detecção do LoL)
- Bibliotecas: pynput, pywin32

## Aviso

Use este bot de acordo com os termos de serviço do League of Legends. O autor não se responsabiliza pelo uso inadequado da ferramenta.
